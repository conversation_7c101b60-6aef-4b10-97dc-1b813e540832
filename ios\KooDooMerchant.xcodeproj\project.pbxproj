// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* KooDooMerchantTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* KooDooMerchantTests.m */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		4FC0547BBB673E9BB7ADB3EF /* Pods_KooDooMerchant.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4747C1790FCFC8FDA9E124D4 /* Pods_KooDooMerchant.framework */; };
		5A7F82918C93442F73D13152 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = E1F8B3B6CA71F9ADA254741F /* PrivacyInfo.xcprivacy */; };
		81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */; };
		E70707312C64B825002C069B /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = E70707302C64B825002C069B /* GoogleService-Info.plist */; };
		E70707462C64C07F002C069B /* AntDesign.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707322C64C07F002C069B /* AntDesign.ttf */; };
		E70707472C64C07F002C069B /* Entypo.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707332C64C07F002C069B /* Entypo.ttf */; };
		E70707482C64C07F002C069B /* EvilIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707342C64C07F002C069B /* EvilIcons.ttf */; };
		E70707492C64C07F002C069B /* Feather.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707352C64C07F002C069B /* Feather.ttf */; };
		E707074A2C64C07F002C069B /* FontAwesome.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707362C64C07F002C069B /* FontAwesome.ttf */; };
		E707074B2C64C07F002C069B /* FontAwesome5_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707372C64C07F002C069B /* FontAwesome5_Brands.ttf */; };
		E707074C2C64C07F002C069B /* FontAwesome5_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707382C64C07F002C069B /* FontAwesome5_Regular.ttf */; };
		E707074D2C64C07F002C069B /* FontAwesome5_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707392C64C07F002C069B /* FontAwesome5_Solid.ttf */; };
		E707074E2C64C07F002C069B /* FontAwesome6_Brands.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E707073A2C64C07F002C069B /* FontAwesome6_Brands.ttf */; };
		E707074F2C64C07F002C069B /* FontAwesome6_Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E707073B2C64C07F002C069B /* FontAwesome6_Regular.ttf */; };
		E70707502C64C07F002C069B /* FontAwesome6_Solid.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E707073C2C64C07F002C069B /* FontAwesome6_Solid.ttf */; };
		E70707512C64C07F002C069B /* Fontisto.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E707073D2C64C07F002C069B /* Fontisto.ttf */; };
		E70707522C64C07F002C069B /* Foundation.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E707073E2C64C07F002C069B /* Foundation.ttf */; };
		E70707532C64C07F002C069B /* Ionicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E707073F2C64C07F002C069B /* Ionicons.ttf */; };
		E70707542C64C07F002C069B /* MaterialCommunityIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707402C64C07F002C069B /* MaterialCommunityIcons.ttf */; };
		E70707552C64C07F002C069B /* MaterialIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707412C64C07F002C069B /* MaterialIcons.ttf */; };
		E70707562C64C07F002C069B /* Octicons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707422C64C07F002C069B /* Octicons.ttf */; };
		E70707572C64C07F002C069B /* SimpleLineIcons.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707432C64C07F002C069B /* SimpleLineIcons.ttf */; };
		E70707582C64C07F002C069B /* Zocial.ttf in Resources */ = {isa = PBXBuildFile; fileRef = E70707442C64C07F002C069B /* Zocial.ttf */; };
		F92772C8075AD53ABAAB8FC0 /* Pods_KooDooMerchant_KooDooMerchantTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B91F13C771615C094CB1D816 /* Pods_KooDooMerchant_KooDooMerchantTests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = KooDooMerchant;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* KooDooMerchantTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = KooDooMerchantTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* KooDooMerchantTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = KooDooMerchantTests.m; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* KooDooMerchant.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = KooDooMerchant.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = KooDooMerchant/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = KooDooMerchant/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = KooDooMerchant/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = KooDooMerchant/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = KooDooMerchant/main.m; sourceTree = "<group>"; };
		13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PrivacyInfo.xcprivacy; path = KooDooMerchant/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		3B4392A12AC88292D35C810B /* Pods-KooDooMerchant.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-KooDooMerchant.debug.xcconfig"; path = "Target Support Files/Pods-KooDooMerchant/Pods-KooDooMerchant.debug.xcconfig"; sourceTree = "<group>"; };
		4747C1790FCFC8FDA9E124D4 /* Pods_KooDooMerchant.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_KooDooMerchant.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		5709B34CF0A7D63546082F79 /* Pods-KooDooMerchant.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-KooDooMerchant.release.xcconfig"; path = "Target Support Files/Pods-KooDooMerchant/Pods-KooDooMerchant.release.xcconfig"; sourceTree = "<group>"; };
		5B7EB9410499542E8C5724F5 /* Pods-KooDooMerchant-KooDooMerchantTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-KooDooMerchant-KooDooMerchantTests.debug.xcconfig"; path = "Target Support Files/Pods-KooDooMerchant-KooDooMerchantTests/Pods-KooDooMerchant-KooDooMerchantTests.debug.xcconfig"; sourceTree = "<group>"; };
		81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = LaunchScreen.storyboard; path = KooDooMerchant/LaunchScreen.storyboard; sourceTree = "<group>"; };
		89C6BE57DB24E9ADA2F236DE /* Pods-KooDooMerchant-KooDooMerchantTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-KooDooMerchant-KooDooMerchantTests.release.xcconfig"; path = "Target Support Files/Pods-KooDooMerchant-KooDooMerchantTests/Pods-KooDooMerchant-KooDooMerchantTests.release.xcconfig"; sourceTree = "<group>"; };
		B91F13C771615C094CB1D816 /* Pods_KooDooMerchant_KooDooMerchantTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_KooDooMerchant_KooDooMerchantTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		E1F8B3B6CA71F9ADA254741F /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xml; name = PrivacyInfo.xcprivacy; path = KooDooMerchant/PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		E70707302C64B825002C069B /* GoogleService-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "GoogleService-Info.plist"; path = "KooDooMerchant/GoogleService-Info.plist"; sourceTree = "<group>"; };
		E70707322C64C07F002C069B /* AntDesign.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = AntDesign.ttf; sourceTree = "<group>"; };
		E70707332C64C07F002C069B /* Entypo.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Entypo.ttf; sourceTree = "<group>"; };
		E70707342C64C07F002C069B /* EvilIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = EvilIcons.ttf; sourceTree = "<group>"; };
		E70707352C64C07F002C069B /* Feather.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Feather.ttf; sourceTree = "<group>"; };
		E70707362C64C07F002C069B /* FontAwesome.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome.ttf; sourceTree = "<group>"; };
		E70707372C64C07F002C069B /* FontAwesome5_Brands.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Brands.ttf; sourceTree = "<group>"; };
		E70707382C64C07F002C069B /* FontAwesome5_Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Regular.ttf; sourceTree = "<group>"; };
		E70707392C64C07F002C069B /* FontAwesome5_Solid.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome5_Solid.ttf; sourceTree = "<group>"; };
		E707073A2C64C07F002C069B /* FontAwesome6_Brands.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome6_Brands.ttf; sourceTree = "<group>"; };
		E707073B2C64C07F002C069B /* FontAwesome6_Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome6_Regular.ttf; sourceTree = "<group>"; };
		E707073C2C64C07F002C069B /* FontAwesome6_Solid.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = FontAwesome6_Solid.ttf; sourceTree = "<group>"; };
		E707073D2C64C07F002C069B /* Fontisto.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Fontisto.ttf; sourceTree = "<group>"; };
		E707073E2C64C07F002C069B /* Foundation.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Foundation.ttf; sourceTree = "<group>"; };
		E707073F2C64C07F002C069B /* Ionicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Ionicons.ttf; sourceTree = "<group>"; };
		E70707402C64C07F002C069B /* MaterialCommunityIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = MaterialCommunityIcons.ttf; sourceTree = "<group>"; };
		E70707412C64C07F002C069B /* MaterialIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = MaterialIcons.ttf; sourceTree = "<group>"; };
		E70707422C64C07F002C069B /* Octicons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Octicons.ttf; sourceTree = "<group>"; };
		E70707432C64C07F002C069B /* SimpleLineIcons.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = SimpleLineIcons.ttf; sourceTree = "<group>"; };
		E70707442C64C07F002C069B /* Zocial.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = Zocial.ttf; sourceTree = "<group>"; };
		E786DC392C64B6AC0081FB5D /* KooDooMerchant.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = KooDooMerchant.entitlements; path = KooDooMerchant/KooDooMerchant.entitlements; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				F92772C8075AD53ABAAB8FC0 /* Pods_KooDooMerchant_KooDooMerchantTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				4FC0547BBB673E9BB7ADB3EF /* Pods_KooDooMerchant.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* KooDooMerchantTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* KooDooMerchantTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = KooDooMerchantTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* KooDooMerchant */ = {
			isa = PBXGroup;
			children = (
				E786DC392C64B6AC0081FB5D /* KooDooMerchant.entitlements */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				81AB9BB72411601600AC10FF /* LaunchScreen.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				13B07FB81A68108700A75B9A /* PrivacyInfo.xcprivacy */,
				E1F8B3B6CA71F9ADA254741F /* PrivacyInfo.xcprivacy */,
			);
			name = KooDooMerchant;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				4747C1790FCFC8FDA9E124D4 /* Pods_KooDooMerchant.framework */,
				B91F13C771615C094CB1D816 /* Pods_KooDooMerchant_KooDooMerchantTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				E70707452C64C07F002C069B /* Fonts */,
				E70707302C64B825002C069B /* GoogleService-Info.plist */,
				13B07FAE1A68108700A75B9A /* KooDooMerchant */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* KooDooMerchantTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* KooDooMerchant.app */,
				00E356EE1AD99517003FC87E /* KooDooMerchantTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				3B4392A12AC88292D35C810B /* Pods-KooDooMerchant.debug.xcconfig */,
				5709B34CF0A7D63546082F79 /* Pods-KooDooMerchant.release.xcconfig */,
				5B7EB9410499542E8C5724F5 /* Pods-KooDooMerchant-KooDooMerchantTests.debug.xcconfig */,
				89C6BE57DB24E9ADA2F236DE /* Pods-KooDooMerchant-KooDooMerchantTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		E70707452C64C07F002C069B /* Fonts */ = {
			isa = PBXGroup;
			children = (
				E70707322C64C07F002C069B /* AntDesign.ttf */,
				E70707332C64C07F002C069B /* Entypo.ttf */,
				E70707342C64C07F002C069B /* EvilIcons.ttf */,
				E70707352C64C07F002C069B /* Feather.ttf */,
				E70707362C64C07F002C069B /* FontAwesome.ttf */,
				E70707372C64C07F002C069B /* FontAwesome5_Brands.ttf */,
				E70707382C64C07F002C069B /* FontAwesome5_Regular.ttf */,
				E70707392C64C07F002C069B /* FontAwesome5_Solid.ttf */,
				E707073A2C64C07F002C069B /* FontAwesome6_Brands.ttf */,
				E707073B2C64C07F002C069B /* FontAwesome6_Regular.ttf */,
				E707073C2C64C07F002C069B /* FontAwesome6_Solid.ttf */,
				E707073D2C64C07F002C069B /* Fontisto.ttf */,
				E707073E2C64C07F002C069B /* Foundation.ttf */,
				E707073F2C64C07F002C069B /* Ionicons.ttf */,
				E70707402C64C07F002C069B /* MaterialCommunityIcons.ttf */,
				E70707412C64C07F002C069B /* MaterialIcons.ttf */,
				E70707422C64C07F002C069B /* Octicons.ttf */,
				E70707432C64C07F002C069B /* SimpleLineIcons.ttf */,
				E70707442C64C07F002C069B /* Zocial.ttf */,
			);
			name = Fonts;
			path = "../node_modules/react-native-vector-icons/Fonts";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* KooDooMerchantTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "KooDooMerchantTests" */;
			buildPhases = (
				A55EABD7B0C7F3A422A6CC61 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				F6A41C54EA430FDDC6A6ED99 /* [CP] Copy Pods Resources */,
				790BC71C36D347B9DCAB0191 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = KooDooMerchantTests;
			productName = KooDooMerchantTests;
			productReference = 00E356EE1AD99517003FC87E /* KooDooMerchantTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* KooDooMerchant */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "KooDooMerchant" */;
			buildPhases = (
				C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				E235C05ADACE081382539298 /* [CP] Copy Pods Resources */,
				37291F21263565FE448F89DC /* [CP-User] [RNFB] Core Configuration */,
				B09E37EE4646184E1300F28F /* [CP-User] [RNFB] Crashlytics Configuration */,
				97F08E0E11ACE1AE0BF48479 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = KooDooMerchant;
			productName = KooDooMerchant;
			productReference = 13B07F961A680F5B00A75B9A /* KooDooMerchant.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1210;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "KooDooMerchant" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* KooDooMerchant */,
				00E356ED1AD99517003FC87E /* KooDooMerchantTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				81AB9BB82411601600AC10FF /* LaunchScreen.storyboard in Resources */,
				E70707472C64C07F002C069B /* Entypo.ttf in Resources */,
				E70707532C64C07F002C069B /* Ionicons.ttf in Resources */,
				E70707312C64B825002C069B /* GoogleService-Info.plist in Resources */,
				E707074F2C64C07F002C069B /* FontAwesome6_Regular.ttf in Resources */,
				E70707502C64C07F002C069B /* FontAwesome6_Solid.ttf in Resources */,
				E707074C2C64C07F002C069B /* FontAwesome5_Regular.ttf in Resources */,
				E70707482C64C07F002C069B /* EvilIcons.ttf in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				E707074A2C64C07F002C069B /* FontAwesome.ttf in Resources */,
				E70707562C64C07F002C069B /* Octicons.ttf in Resources */,
				5A7F82918C93442F73D13152 /* PrivacyInfo.xcprivacy in Resources */,
				E70707492C64C07F002C069B /* Feather.ttf in Resources */,
				E70707552C64C07F002C069B /* MaterialIcons.ttf in Resources */,
				E707074D2C64C07F002C069B /* FontAwesome5_Solid.ttf in Resources */,
				E70707572C64C07F002C069B /* SimpleLineIcons.ttf in Resources */,
				E70707462C64C07F002C069B /* AntDesign.ttf in Resources */,
				E707074E2C64C07F002C069B /* FontAwesome6_Brands.ttf in Resources */,
				E70707542C64C07F002C069B /* MaterialCommunityIcons.ttf in Resources */,
				E70707522C64C07F002C069B /* Foundation.ttf in Resources */,
				E70707512C64C07F002C069B /* Fontisto.ttf in Resources */,
				E70707582C64C07F002C069B /* Zocial.ttf in Resources */,
				E707074B2C64C07F002C069B /* FontAwesome5_Brands.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		37291F21263565FE448F89DC /* [CP-User] [RNFB] Core Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Core Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\n\n##########################################################################\n##########################################################################\n#\n#  NOTE THAT IF YOU CHANGE THIS FILE YOU MUST RUN pod install AFTERWARDS\n#\n#  This file is installed as an Xcode build script in the project file\n#  by cocoapods, and you will not see your changes until you pod install\n#\n##########################################################################\n##########################################################################\n\nset -e\n\n_MAX_LOOKUPS=2;\n_SEARCH_RESULT=''\n_RN_ROOT_EXISTS=''\n_CURRENT_LOOKUPS=1\n_JSON_ROOT=\"'react-native'\"\n_JSON_FILE_NAME='firebase.json'\n_JSON_OUTPUT_BASE64='e30=' # { }\n_CURRENT_SEARCH_DIR=${PROJECT_DIR}\n_PLIST_BUDDY=/usr/libexec/PlistBuddy\n_TARGET_PLIST=\"${BUILT_PRODUCTS_DIR}/${INFOPLIST_PATH}\"\n_DSYM_PLIST=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist\"\n\n# plist arrays\n_PLIST_ENTRY_KEYS=()\n_PLIST_ENTRY_TYPES=()\n_PLIST_ENTRY_VALUES=()\n\nfunction setPlistValue {\n  echo \"info:      setting plist entry '$1' of type '$2' in file '$4'\"\n  ${_PLIST_BUDDY} -c \"Add :$1 $2 '$3'\" $4 || echo \"info:      '$1' already exists\"\n}\n\nfunction getFirebaseJsonKeyValue () {\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$1'); puts output[$_JSON_ROOT]['$2']\"\n  else\n    echo \"\"\n  fi;\n}\n\nfunction jsonBoolToYesNo () {\n  if [[ $1 == \"false\" ]]; then\n    echo \"NO\"\n  elif [[ $1 == \"true\" ]]; then\n    echo \"YES\"\n  else echo \"NO\"\n  fi\n}\n\necho \"info: -> RNFB build script started\"\necho \"info: 1) Locating ${_JSON_FILE_NAME} file:\"\n\nif [[ -z ${_CURRENT_SEARCH_DIR} ]]; then\n  _CURRENT_SEARCH_DIR=$(pwd)\nfi;\n\nwhile true; do\n  _CURRENT_SEARCH_DIR=$(dirname \"$_CURRENT_SEARCH_DIR\")\n  if [[ \"$_CURRENT_SEARCH_DIR\" == \"/\" ]] || [[ ${_CURRENT_LOOKUPS} -gt ${_MAX_LOOKUPS} ]]; then break; fi;\n  echo \"info:      ($_CURRENT_LOOKUPS of $_MAX_LOOKUPS) Searching in '$_CURRENT_SEARCH_DIR' for a ${_JSON_FILE_NAME} file.\"\n  _SEARCH_RESULT=$(find \"$_CURRENT_SEARCH_DIR\" -maxdepth 2 -name ${_JSON_FILE_NAME} -print | /usr/bin/head -n 1)\n  if [[ ${_SEARCH_RESULT} ]]; then\n    echo \"info:      ${_JSON_FILE_NAME} found at $_SEARCH_RESULT\"\n    break;\n  fi;\n  _CURRENT_LOOKUPS=$((_CURRENT_LOOKUPS+1))\ndone\n\nif [[ ${_SEARCH_RESULT} ]]; then\n  _JSON_OUTPUT_RAW=$(cat \"${_SEARCH_RESULT}\")\n  _RN_ROOT_EXISTS=$(ruby -Ku -e \"require 'rubygems';require 'json'; output=JSON.parse('$_JSON_OUTPUT_RAW'); puts output[$_JSON_ROOT]\" || echo '')\n\n  if [[ ${_RN_ROOT_EXISTS} ]]; then\n    if ! python3 --version >/dev/null 2>&1; then echo \"python3 not found, firebase.json file processing error.\" && exit 1; fi\n    _JSON_OUTPUT_BASE64=$(python3 -c 'import json,sys,base64;print(base64.b64encode(bytes(json.dumps(json.loads(open('\"'${_SEARCH_RESULT}'\"', '\"'rb'\"').read())['${_JSON_ROOT}']), '\"'utf-8'\"')).decode())' || echo \"e30=\")\n  fi\n\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n\n  # config.app_data_collection_default_enabled\n  _APP_DATA_COLLECTION_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_data_collection_default_enabled\")\n  if [[ $_APP_DATA_COLLECTION_ENABLED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseDataCollectionDefaultEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_DATA_COLLECTION_ENABLED\")\")\n  fi\n\n  # config.analytics_auto_collection_enabled\n  _ANALYTICS_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_auto_collection_enabled\")\n  if [[ $_ANALYTICS_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_COLLECTION\")\")\n  fi\n\n  # config.analytics_collection_deactivated\n  _ANALYTICS_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_collection_deactivated\")\n  if [[ $_ANALYTICS_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FIREBASE_ANALYTICS_COLLECTION_DEACTIVATED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_DEACTIVATED\")\")\n  fi\n\n  # config.analytics_idfv_collection_enabled\n  _ANALYTICS_IDFV_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_idfv_collection_enabled\")\n  if [[ $_ANALYTICS_IDFV_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_IDFV_COLLECTION_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_IDFV_COLLECTION\")\")\n  fi\n\n  # config.analytics_default_allow_analytics_storage\n  _ANALYTICS_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_analytics_storage\")\n  if [[ $_ANALYTICS_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_ANALYTICS_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_storage\n  _ANALYTICS_AD_STORAGE=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_storage\")\n  if [[ $_ANALYTICS_AD_STORAGE ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_STORAGE\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_STORAGE\")\")\n  fi\n\n  # config.analytics_default_allow_ad_user_data\n  _ANALYTICS_AD_USER_DATA=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_user_data\")\n  if [[ $_ANALYTICS_AD_USER_DATA ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_USER_DATA\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AD_USER_DATA\")\")\n  fi\n\n  # config.analytics_default_allow_ad_personalization_signals\n  _ANALYTICS_PERSONALIZATION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"analytics_default_allow_ad_personalization_signals\")\n  if [[ $_ANALYTICS_PERSONALIZATION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_DEFAULT_ALLOW_AD_PERSONALIZATION_SIGNALS\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_PERSONALIZATION\")\")\n  fi\n\n  # config.analytics_registration_with_ad_network_enabled\n  _ANALYTICS_REGISTRATION_WITH_AD_NETWORK=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_registration_with_ad_network_enabled\")\n  if [[ $_ANALYTICS_REGISTRATION_WITH_AD_NETWORK ]]; then\n    _PLIST_ENTRY_KEYS+=(\"GOOGLE_ANALYTICS_REGISTRATION_WITH_AD_NETWORK_ENABLED\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_REGISTRATION_WITH_AD_NETWORK\")\")\n  fi\n\n  # config.google_analytics_automatic_screen_reporting_enabled\n  _ANALYTICS_AUTO_SCREEN_REPORTING=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"google_analytics_automatic_screen_reporting_enabled\")\n  if [[ $_ANALYTICS_AUTO_SCREEN_REPORTING ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAutomaticScreenReportingEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_ANALYTICS_AUTO_SCREEN_REPORTING\")\")\n  fi\n\n  # config.perf_auto_collection_enabled\n  _PERF_AUTO_COLLECTION=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_auto_collection_enabled\")\n  if [[ $_PERF_AUTO_COLLECTION ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_enabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_AUTO_COLLECTION\")\")\n  fi\n\n  # config.perf_collection_deactivated\n  _PERF_DEACTIVATED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"perf_collection_deactivated\")\n  if [[ $_PERF_DEACTIVATED ]]; then\n    _PLIST_ENTRY_KEYS+=(\"firebase_performance_collection_deactivated\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_PERF_DEACTIVATED\")\")\n  fi\n\n  # config.messaging_auto_init_enabled\n  _MESSAGING_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"messaging_auto_init_enabled\")\n  if [[ $_MESSAGING_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseMessagingAutoInitEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_MESSAGING_AUTO_INIT\")\")\n  fi\n\n  # config.in_app_messaging_auto_colllection_enabled\n  _FIAM_AUTO_INIT=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"in_app_messaging_auto_collection_enabled\")\n  if [[ $_FIAM_AUTO_INIT ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseInAppMessagingAutomaticDataCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_FIAM_AUTO_INIT\")\")\n  fi\n\n  # config.app_check_token_auto_refresh\n  _APP_CHECK_TOKEN_AUTO_REFRESH=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"app_check_token_auto_refresh\")\n  if [[ $_APP_CHECK_TOKEN_AUTO_REFRESH ]]; then\n    _PLIST_ENTRY_KEYS+=(\"FirebaseAppCheckTokenAutoRefreshEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"$(jsonBoolToYesNo \"$_APP_CHECK_TOKEN_AUTO_REFRESH\")\")\n  fi\n\n  # config.crashlytics_disable_auto_disabler - undocumented for now - mainly for debugging, document if becomes useful\n  _CRASHLYTICS_AUTO_DISABLE_ENABLED=$(getFirebaseJsonKeyValue \"$_JSON_OUTPUT_RAW\" \"crashlytics_disable_auto_disabler\")\n  if [[ $_CRASHLYTICS_AUTO_DISABLE_ENABLED == \"true\" ]]; then\n    echo \"Disabled Crashlytics auto disabler.\" # do nothing\n  else\n    _PLIST_ENTRY_KEYS+=(\"FirebaseCrashlyticsCollectionEnabled\")\n    _PLIST_ENTRY_TYPES+=(\"bool\")\n    _PLIST_ENTRY_VALUES+=(\"NO\")\n  fi\nelse\n  _PLIST_ENTRY_KEYS+=(\"firebase_json_raw\")\n  _PLIST_ENTRY_TYPES+=(\"string\")\n  _PLIST_ENTRY_VALUES+=(\"$_JSON_OUTPUT_BASE64\")\n  echo \"warning:   A firebase.json file was not found, whilst this file is optional it is recommended to include it to configure firebase services in React Native Firebase.\"\nfi;\n\necho \"info: 2) Injecting Info.plist entries: \"\n\n# Log out the keys we're adding\nfor i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n  echo \"    ->  $i) ${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\"\ndone\n\nfor plist in \"${_TARGET_PLIST}\" \"${_DSYM_PLIST}\" ; do\n  if [[ -f \"${plist}\" ]]; then\n\n    # paths with spaces break the call to setPlistValue. temporarily modify\n    # the shell internal field separator variable (IFS), which normally\n    # includes spaces, to consist only of line breaks\n    oldifs=$IFS\n    IFS=\"\n\"\n\n    for i in \"${!_PLIST_ENTRY_KEYS[@]}\"; do\n      setPlistValue \"${_PLIST_ENTRY_KEYS[$i]}\" \"${_PLIST_ENTRY_TYPES[$i]}\" \"${_PLIST_ENTRY_VALUES[$i]}\" \"${plist}\"\n    done\n\n    # restore the original internal field separator value\n    IFS=$oldifs\n  else\n    echo \"warning:   A Info.plist build output file was not found (${plist})\"\n  fi\ndone\n\necho \"info: <- RNFB build script finished\"\n";
		};
		790BC71C36D347B9DCAB0191 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant-KooDooMerchantTests/Pods-KooDooMerchant-KooDooMerchantTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant-KooDooMerchantTests/Pods-KooDooMerchant-KooDooMerchantTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant-KooDooMerchantTests/Pods-KooDooMerchant-KooDooMerchantTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		97F08E0E11ACE1AE0BF48479 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant/Pods-KooDooMerchant-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant/Pods-KooDooMerchant-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant/Pods-KooDooMerchant-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		A55EABD7B0C7F3A422A6CC61 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-KooDooMerchant-KooDooMerchantTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B09E37EE4646184E1300F28F /* [CP-User] [RNFB] Crashlytics Configuration */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${TARGET_NAME}",
				"$(BUILT_PRODUCTS_DIR)/$(INFOPLIST_PATH)",
			);
			name = "[CP-User] [RNFB] Crashlytics Configuration";
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n#\n# Copyright (c) 2016-present Invertase Limited & Contributors\n#\n# Licensed under the Apache License, Version 2.0 (the \"License\");\n# you may not use this library except in compliance with the License.\n# You may obtain a copy of the License at\n#\n#   http://www.apache.org/licenses/LICENSE-2.0\n#\n# Unless required by applicable law or agreed to in writing, software\n# distributed under the License is distributed on an \"AS IS\" BASIS,\n# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n# See the License for the specific language governing permissions and\n# limitations under the License.\n#\nset -e\n\nif [[ ${PODS_ROOT} ]]; then\n  echo \"info: Exec FirebaseCrashlytics Run from Pods\"\n  \"${PODS_ROOT}/FirebaseCrashlytics/run\"\nelse\n  echo \"info: Exec FirebaseCrashlytics Run from framework\"\n  \"${PROJECT_DIR}/FirebaseCrashlytics.framework/run\"\nfi\n";
		};
		C38B50BA6285516D6DCD4F65 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-KooDooMerchant-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E235C05ADACE081382539298 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant/Pods-KooDooMerchant-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant/Pods-KooDooMerchant-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant/Pods-KooDooMerchant-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		F6A41C54EA430FDDC6A6ED99 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant-KooDooMerchantTests/Pods-KooDooMerchant-KooDooMerchantTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant-KooDooMerchantTests/Pods-KooDooMerchant-KooDooMerchantTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-KooDooMerchant-KooDooMerchantTests/Pods-KooDooMerchant-KooDooMerchantTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				00E356F31AD99517003FC87E /* KooDooMerchantTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* KooDooMerchant */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5B7EB9410499542E8C5724F5 /* Pods-KooDooMerchant-KooDooMerchantTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/GPrinterLegacy/GSDK",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = KooDooMerchantTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/KooDooMerchant.app/KooDooMerchant";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 89C6BE57DB24E9ADA2F236DE /* Pods-KooDooMerchant-KooDooMerchantTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/Frameworks/GPrinterLegacy/GSDK",
				);
				INFOPLIST_FILE = KooDooMerchantTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "org.reactjs.native.example.$(PRODUCT_NAME:rfc1034identifier)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/KooDooMerchant.app/KooDooMerchant";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3B4392A12AC88292D35C810B /* Pods-KooDooMerchant.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = KooDooMerchant/KooDooMerchant.entitlements;
				CURRENT_PROJECT_VERSION = 796;
				DEVELOPMENT_TEAM = P4C58C9XL4;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BEMCheckBox\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BoringSSL-GRPC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseABTesting\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDatabase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebasePerformance\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfig\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCCheckbox\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPushNotificationIOS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNExternalDisplay\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBCrashlytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBDatabase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBFirestore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBPerf\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFastImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileLogger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFlashList\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNQrGenerator\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNZipArchive\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-hermes\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-utils\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/WatermelonDB\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ZXingObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/abseil\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/dr-pogodin-react-native-fs\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-C++\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libwebp\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/molpay-mobile-xdk-reactnative-beta\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-background-actions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-get-random-values\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-html-to-pdf\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-multiple-modals\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-ping\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-tcp-socket\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-thermal-receipt-printer-image-qr\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/rn-fetch-blob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/simdjson\"",
					"\"${PODS_ROOT}/../../node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/Frameworks\"",
					"\"${PODS_ROOT}/FirebaseAnalytics/Frameworks\"",
					"\"${PODS_ROOT}/GoogleAppMeasurement/Frameworks\"",
					"\"${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/AdIdSupport\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/AdIdSupport\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/WithoutAdIdSupport\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/react-native-thermal-receipt-printer-image-qr\"",
					"\"${PODS_ROOT}/../ios/Frameworks\"",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/Frameworks",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/GPrinterLegacy/GSDK",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BEMCheckBox/BEMCheckBox.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BoringSSL-GRPC/openssl_grpc.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack/CocoaLumberjack.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion/DoubleConversion.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseABTesting/FirebaseABTesting.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop/FirebaseAppCheckInterop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth/FirebaseAuth.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop/FirebaseAuthInterop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension/FirebaseCoreExtension.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDatabase/FirebaseDatabase.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestore/FirebaseFirestore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestoreInternal/FirebaseFirestoreInternal.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebasePerformance/FirebasePerformance.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfig/FirebaseRemoteConfig.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions/FirebaseSessions.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift/FirebaseSharedSwift.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseStorage/FirebaseStorage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher/GTMSessionFetcher.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift/Promises.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/folly.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation/RCTDeprecation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety/RCTTypeSafety.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCCheckbox/RNCCheckbox.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard/RNCClipboard.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker/RNCPicker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPushNotificationIOS/RNCPushNotificationIOS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker/RNDateTimePicker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo/RNDeviceInfo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNExternalDisplay/RNExternalDisplay.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics/RNFBAnalytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp/RNFBApp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAuth/RNFBAuth.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBCrashlytics/RNFBCrashlytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBDatabase/RNFBDatabase.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBFirestore/RNFBFirestore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging/RNFBMessaging.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBPerf/RNFBPerf.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBStorage/RNFBStorage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFastImage/RNFastImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileLogger/RNFileLogger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFlashList/RNFlashList.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler/RNGestureHandler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNQrGenerator/RNQrGenerator.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated/RNReanimated.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVG.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens/RNScreens.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNZipArchive/RNZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen/React_Codegen.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules/CoreModules.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage/React_FabricImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager/React_ImageManager.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer/React_Mapbuffer.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation/RCTAnimation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate/React_RCTAppDelegate.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob/RCTBlob.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric/RCTFabric.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage/RCTImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking/RCTLinking.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork/RCTNetwork.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings/RCTSettings.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText/RCTText.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration/RCTVibration.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple/React_RuntimeApple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore/React_RuntimeCore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes/React_RuntimeHermes.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/cxxreact.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-hermes/reacthermes.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler/React_jserrorhandler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi/jsi.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor/jsireact.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger/logger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig/React_nativeconfig.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger/reactperflogger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler/React_runtimescheduler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-utils/React_utils.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop/RecaptchaInterop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive/SSZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket/SocketRocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/WatermelonDB/WatermelonDB.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga/yoga.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ZXingObjC/ZXingObjC.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/abseil/absl.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/dr-pogodin-react-native-fs/dr_pogodin_react_native_fs.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt/fmt.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-C++/grpcpp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-Core/grpc.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library/leveldb.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libwebp/libwebp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/molpay-mobile-xdk-reactnative-beta/molpay_mobile_xdk_reactnative_beta.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-background-actions/react_native_background_actions.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker/react_native_document_picker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-get-random-values/react_native_get_random_values.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-html-to-pdf/react_native_html_to_pdf.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker/react_native_image_picker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-multiple-modals/react_native_multiple_modals.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo/react_native_netinfo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker/react_native_orientation_locker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-ping/react_native_ping.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context/react_native_safe_area_context.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-tcp-socket/react_native_tcp_socket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-thermal-receipt-printer-image-qr/react_native_thermal_receipt_printer_image_qr.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview/react_native_webview.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/rn-fetch-blob/rn_fetch_blob.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/simdjson/simdjson.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/Headers/Public/React-hermes\"",
					"\"$(PODS_ROOT)/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"${PODS_ROOT}/../ios/PrinterSDK\"/**",
					"\"${PODS_ROOT}/../../../../ios/Frameworks/IOS_SWIFT_WIFI_SDK.xcframework/ios-arm64/Headers\"",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/IOS_SWIFT_WIFI_SDK.xcframework/ios-arm64_x86_64-simulator/IOS_SWIFT_WIFI_SDK.framework/Headers",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/IOS_SWIFT_WIFI_SDK.xcframework/ios-arm64_x86_64-maccatalyst/IOS_SWIFT_WIFI_SDK.framework/Headers",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/IOS_SWIFT_WIFI_SDK.xcframework/ios-arm64/IOS_SWIFT_WIFI_SDK.framework/Headers",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/GPrinterLegacy/GSDK/GSDK.framework/Headers",
				);
				INFOPLIST_FILE = KooDooMerchant/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "KooDoo Merchant";
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.4.248;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.mykoodoo.merchant;
				PRODUCT_NAME = KooDooMerchant;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5709B34CF0A7D63546082F79 /* Pods-KooDooMerchant.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = KooDooMerchant/KooDooMerchant.entitlements;
				CURRENT_PROJECT_VERSION = 796;
				DEVELOPMENT_TEAM = P4C58C9XL4;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BEMCheckBox\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BoringSSL-GRPC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseABTesting\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDatabase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebasePerformance\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfig\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCCheckbox\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPushNotificationIOS\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNExternalDisplay\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBCrashlytics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBDatabase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBFirestore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBPerf\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBStorage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFastImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileLogger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFlashList\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNQrGenerator\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNZipArchive\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-hermes\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-utils\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/WatermelonDB\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ZXingObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/abseil\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/dr-pogodin-react-native-fs\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-C++\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libwebp\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/molpay-mobile-xdk-reactnative-beta\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-background-actions\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-get-random-values\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-html-to-pdf\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-multiple-modals\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-ping\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-tcp-socket\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-thermal-receipt-printer-image-qr\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/rn-fetch-blob\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/simdjson\"",
					"\"${PODS_ROOT}/../../node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/Frameworks\"",
					"\"${PODS_ROOT}/FirebaseAnalytics/Frameworks\"",
					"\"${PODS_ROOT}/GoogleAppMeasurement/Frameworks\"",
					"\"${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/FirebaseAnalytics/AdIdSupport\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/AdIdSupport\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/GoogleAppMeasurement/WithoutAdIdSupport\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built\"",
					"\"${PODS_XCFRAMEWORKS_BUILD_DIR}/react-native-thermal-receipt-printer-image-qr\"",
					"\"${PODS_ROOT}/../ios/Frameworks\"",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/node_modules/@conodene/react-native-thermal-receipt-printer-image-qr/ios/Frameworks",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/GPrinterLegacy/GSDK",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BEMCheckBox/BEMCheckBox.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BoringSSL-GRPC/openssl_grpc.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaAsyncSocket/CocoaAsyncSocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/CocoaLumberjack/CocoaLumberjack.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion/DoubleConversion.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseABTesting/FirebaseABTesting.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop/FirebaseAppCheckInterop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth/FirebaseAuth.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop/FirebaseAuthInterop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore/FirebaseCore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension/FirebaseCoreExtension.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCrashlytics/FirebaseCrashlytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseDatabase/FirebaseDatabase.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestore/FirebaseFirestore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestoreInternal/FirebaseFirestoreInternal.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseInstallations/FirebaseInstallations.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseMessaging/FirebaseMessaging.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebasePerformance/FirebasePerformance.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfig/FirebaseRemoteConfig.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSessions/FirebaseSessions.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift/FirebaseSharedSwift.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseStorage/FirebaseStorage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher/GTMSessionFetcher.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleDataTransport/GoogleDataTransport.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities/GoogleUtilities.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC/FBLPromises.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesSwift/Promises.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly/folly.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation/RCTDeprecation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety/RCTTypeSafety.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCAsyncStorage/RNCAsyncStorage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCCheckbox/RNCCheckbox.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCClipboard/RNCClipboard.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPicker/RNCPicker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNCPushNotificationIOS/RNCPushNotificationIOS.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDateTimePicker/RNDateTimePicker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNDeviceInfo/RNDeviceInfo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNExternalDisplay/RNExternalDisplay.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAnalytics/RNFBAnalytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBApp/RNFBApp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBAuth/RNFBAuth.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBCrashlytics/RNFBCrashlytics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBDatabase/RNFBDatabase.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBFirestore/RNFBFirestore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBMessaging/RNFBMessaging.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBPerf/RNFBPerf.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFBStorage/RNFBStorage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFastImage/RNFastImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFileLogger/RNFileLogger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNFlashList/RNFlashList.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNGestureHandler/RNGestureHandler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNQrGenerator/RNQrGenerator.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNReanimated/RNReanimated.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNSVG/RNSVG.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNScreens/RNScreens.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RNZipArchive/RNZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Codegen/React_Codegen.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Core/React.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules/CoreModules.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage/React_FabricImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager/React_ImageManager.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer/React_Mapbuffer.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation/RCTAnimation.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate/React_RCTAppDelegate.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob/RCTBlob.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric/RCTFabric.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage/RCTImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTLinking/RCTLinking.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork/RCTNetwork.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTSettings/RCTSettings.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText/RCTText.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTVibration/RCTVibration.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple/React_RuntimeApple.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore/React_RuntimeCore.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes/React_RuntimeHermes.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact/cxxreact.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-hermes/reacthermes.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler/React_jserrorhandler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsi/jsi.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor/jsireact.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-logger/logger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig/React_nativeconfig.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger/reactperflogger.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler/React_runtimescheduler.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/React-utils/React_utils.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop/RecaptchaInterop.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImage/SDWebImage.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SSZipArchive/SSZipArchive.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket/SocketRocket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/WatermelonDB/WatermelonDB.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Yoga/yoga.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/ZXingObjC/ZXingObjC.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/abseil/absl.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/dr-pogodin-react-native-fs/dr_pogodin_react_native_fs.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/fmt/fmt.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-C++/grpcpp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-Core/grpc.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/glog/glog.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library/leveldb.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/libwebp/libwebp.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/molpay-mobile-xdk-reactnative-beta/molpay_mobile_xdk_reactnative_beta.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb/nanopb.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-background-actions/react_native_background_actions.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-document-picker/react_native_document_picker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-get-random-values/react_native_get_random_values.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-html-to-pdf/react_native_html_to_pdf.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-image-picker/react_native_image_picker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-multiple-modals/react_native_multiple_modals.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-netinfo/react_native_netinfo.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-orientation-locker/react_native_orientation_locker.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-ping/react_native_ping.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-safe-area-context/react_native_safe_area_context.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-tcp-socket/react_native_tcp_socket.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-thermal-receipt-printer-image-qr/react_native_thermal_receipt_printer_image_qr.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/react-native-webview/react_native_webview.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/rn-fetch-blob/rn_fetch_blob.framework/Headers\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/simdjson/simdjson.framework/Headers\"",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/Firebase\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"$(inherited)",
					"${PODS_ROOT}/Firebase/CoreOnly/Sources",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/boost-for-react-native\"",
					"\"$(PODS_ROOT)/glog\"",
					"\"$(PODS_ROOT)/RCT-Folly\"",
					"\"$(PODS_ROOT)/Headers/Public/React-hermes\"",
					"\"$(PODS_ROOT)/Headers/Public/hermes-engine\"",
					"\"$(PODS_ROOT)/../../node_modules/react-native/ReactCommon\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"${PODS_ROOT}/../ios/PrinterSDK\"/**",
					"\"${PODS_ROOT}/../../../../ios/Frameworks/IOS_SWIFT_WIFI_SDK.xcframework/ios-arm64/Headers\"",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/IOS_SWIFT_WIFI_SDK.xcframework/ios-arm64_x86_64-simulator/IOS_SWIFT_WIFI_SDK.framework/Headers",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/IOS_SWIFT_WIFI_SDK.xcframework/ios-arm64_x86_64-maccatalyst/IOS_SWIFT_WIFI_SDK.framework/Headers",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/IOS_SWIFT_WIFI_SDK.xcframework/ios-arm64/IOS_SWIFT_WIFI_SDK.framework/Headers",
					"/Users/<USER>/koodooapps/koodoo-merchant-v2/ios/Frameworks/GPrinterLegacy/GSDK/GSDK.framework/Headers",
				);
				INFOPLIST_FILE = KooDooMerchant/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "KooDoo Merchant";
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.4.248;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.mykoodoo.merchant;
				PRODUCT_NAME = KooDooMerchant;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				CXX = "";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CC = "";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				CXX = "";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon-Samples/ReactCommon_Samples.framework/Headers/platform/ios",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers",
					"${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 15.5;
				LD = "";
				LDPLUSPLUS = "";
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = "$(inherited)  ";
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "KooDooMerchantTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "KooDooMerchant" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "KooDooMerchant" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
